# Changelog

Tous les changements notables de ce projet seront documentés dans ce fichier.

## [0.2.0] - 2025-10-03

### ✨ Nouvelles fonctionnalités
- ajout du système de sauvergarde automatique de la db

### 🐛 Corrections de bugs
- empêcher npm version de créer automatiquement des tags Git
- corriger les chemins des scripts dans package.json

### 🔧 Autres changements
- chore: release v0.2.0
- 0.2.0
- chore: reset version to 0.1.1 for proper release
- chore: mise à jour des versions et changelog pour v0.2.0
- 0.2.0
- feat : saisie future & filtres-tris en historique & pointage
- docs: update changelog [skip ci]
- feat : améliore structure et docs + modif transaction
- docs: update changelog [skip ci]
- feat : correction du action failed in Github après push
- chore: release v0.1.1

## [0.2.0] - 2025-10-03

### ✨ Nouvelles fonctionnalités
- ajout du système de sauvergarde automatique de la db

### 🐛 Corrections de bugs
- corriger les chemins des scripts dans package.json

### 🔧 Autres changements
- 0.2.0
- chore: reset version to 0.1.1 for proper release
- chore: mise à jour des versions et changelog pour v0.2.0
- 0.2.0
- feat : saisie future & filtres-tris en historique & pointage
- docs: update changelog [skip ci]
- feat : améliore structure et docs + modif transaction
- docs: update changelog [skip ci]
- feat : correction du action failed in Github après push
- chore: release v0.1.1

## [0.2.0] - 2025-10-03

### 🔧 Autres changements
- docs: update changelog [skip ci]

## Derniers changements

- 64cf43c 0.2.0
- 27be02a feat : saisie future & filtres-tris en historique & pointage
- 5d93724 docs: update changelog [skip ci]
- 8b9f327 feat : améliore structure et docs + modif transaction
- a669d72 docs: update changelog [skip ci]
- 1ff13ff feat : correction du action failed in Github après push
- 3667339 feat: ajout du système de sauvergarde automatique de la db
- 5397a8a chore: release v0.1.1
- 4cf1c9a 0.1.1
- 40bcb13 feat: ajout du système de versioning complet
